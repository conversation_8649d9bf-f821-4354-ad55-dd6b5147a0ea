//
//  SubscriptionTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 订阅页面测试视图
 * 用于验证订阅页面的UI/UX是否与ztt1项目完全一致
 */
struct SubscriptionTestView: View {
    
    @State private var showSubscriptionView = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("订阅页面测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("点击下方按钮测试新的订阅页面")
                    .font(.body)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                
                But<PERSON>(action: {
                    showSubscriptionView = true
                }) {
                    HStack {
                        Image(systemName: "crown.fill")
                            .font(.title2)
                        
                        Text("打开订阅页面")
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 15)
                    .background(DesignSystem.Colors.primary)
                    .cornerRadius(25)
                    .shadow(color: DesignSystem.Colors.primary.opacity(0.3), radius: 10, x: 0, y: 5)
                }
                
                VStack(alignment: .leading, spacing: 15) {
                    Text("测试要点：")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        TestCheckItem(text: "用户信息区域显示正确")
                        TestCheckItem(text: "分段选项卡切换正常")
                        TestCheckItem(text: "背景图切换动画流畅")
                        TestCheckItem(text: "价格卡片选择交互正常")
                        TestCheckItem(text: "订阅按钮状态正确")
                        TestCheckItem(text: "协议勾选功能正常")
                        TestCheckItem(text: "返回按钮功能正常")
                        TestCheckItem(text: "iPad适配效果良好")
                    }
                }
                .padding(20)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
                
                Spacer()
            }
            .padding(20)
            .background(DesignSystem.Colors.background)
            .navigationTitle("订阅页面测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .fullScreenCover(isPresented: $showSubscriptionView) {
            SubscriptionView {
                showSubscriptionView = false
            }
        }
    }
}

/**
 * 测试检查项组件
 */
struct TestCheckItem: View {
    let text: String
    
    var body: some View {
        HStack(spacing: 10) {
            Image(systemName: "checkmark.circle")
                .foregroundColor(DesignSystem.Colors.primary)
                .font(.system(size: 16, weight: .medium))
            
            Text(text)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Spacer()
        }
    }
}

// MARK: - Preview
#Preview {
    SubscriptionTestView()
}
