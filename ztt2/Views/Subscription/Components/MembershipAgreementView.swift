//
//  MembershipAgreementView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 会员服务协议视图
 * 显示会员服务协议的详细内容
 */
struct MembershipAgreementView: View {
    
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                    Text("会员服务协议")
                        .font(.system(
                            size: DesignSystem.Typography.HeadingLarge.fontSize,
                            weight: DesignSystem.Typography.HeadingLarge.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.bottom, DesignSystem.Spacing.lg)
                    
                    Text("感谢您选择我们的会员服务。请仔细阅读以下条款和条件：")
                        .font(.system(
                            size: DesignSystem.Typography.Body.fontSize,
                            weight: DesignSystem.Typography.Body.fontWeight
                        ))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    
                    Group {
                        Text("1. 服务内容")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .padding(.top, DesignSystem.Spacing.md)
                        
                        Text("会员服务包括但不限于：解锁高级功能、多设备同步、AI分析报告等。")
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text("2. 付费条款")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .padding(.top, DesignSystem.Spacing.md)
                        
                        Text("会员费用将通过您的Apple ID账户收取。订阅将自动续费，除非您在当前订阅期结束前至少24小时取消。")
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text("3. 取消和退款")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .padding(.top, DesignSystem.Spacing.md)
                        
                        Text("您可以随时在设置中取消订阅。退款政策遵循Apple App Store的相关规定。")
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        Text("4. 隐私保护")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                            .padding(.top, DesignSystem.Spacing.md)
                        
                        Text("我们承诺保护您的隐私，不会将您的个人信息泄露给第三方。")
                            .font(.system(
                                size: DesignSystem.Typography.Body.fontSize,
                                weight: DesignSystem.Typography.Body.fontWeight
                            ))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                }
                .padding(DesignSystem.Spacing.lg)
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(DesignSystem.Colors.primary)
            )
        }
    }
}

// MARK: - Preview
#Preview {
    MembershipAgreementView()
}
