//
//  iPadLayoutConfig.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * iPad专用布局配置
 * 为iPad设备提供优化的布局参数
 */
struct iPadLayoutConfig {
    
    // MARK: - 订阅页面iPad布局配置
    struct SubscriptionPage {
        
        // MARK: - 用户信息区域iPad配置
        struct UserInfoSection {
            static let heightPercentage: CGFloat = 0.33 // iPad减小高度占比
            static let topPositionPercentage: CGFloat = 0.18 // iPad顶部位置调整
            static let horizontalPadding: CGFloat = 80 // iPad增大左右边距
            static let verticalPadding: CGFloat = 35 // iPad增大上下边距
            static let cornerRadius: CGFloat = 20 // iPad增大圆角
            static let titleFontSize: CGFloat = 28 // iPad增大标题字体
            static let subtitleFontSize: CGFloat = 18 // iPad增大副标题字体
            static let iconSize: CGFloat = 50 // iPad增大图标尺寸
            static let backgroundColor = Color(hex: "#edf6d9")
            static let shadowRadius: CGFloat = 12 // iPad增大阴影
            static let shadowOpacity: Double = 0.15 // iPad调整阴影透明度
        }
        
        // MARK: - 分段选项卡iPad配置
        struct MembershipTab {
            static let topOffsetPercentage: CGFloat = 0.30 // iPad调整顶部偏移 - 修改为屏幕高度的30%
            static let offsetX: CGFloat = 40
            static let offsetY: CGFloat = 0
            static let tabWidth: CGFloat = 150 // iPad增大选项卡宽度
            static let tabHeight: CGFloat = 55 // iPad增大选项卡高度
            static let totalWidth: CGFloat = 350 // iPad增大宽度
            static let scaleX: CGFloat = 3.0 // iPad恢复正常比例
            static let scaleY: CGFloat = 2.2 // iPad恢复正常比例
            static let spacing: CGFloat = 10 // iPad增大间距
            static let horizontalPadding: CGFloat = 100 // iPad增大左右边距
            static let tabCornerRadius: CGFloat = 22 // iPad增大圆角
            static let textFontSize: CGFloat = 18 // iPad增大文字
            static let selectedTextOpacity: Double = 0 // iPad恢复文字透明度
            static let unselectedTextOpacity: Double = 0 // iPad调整未选中文字透明度
            static let selectedBackgroundOpacity: Double = 0 // iPad调整选中背景透明度
            static let unselectedBackgroundOpacity: Double = 0 // iPad调整未选中背景透明度
            static let shadowRadius: CGFloat = 8 // iPad增大阴影
            static let shadowOpacity: Double = 0.15 // iPad调整阴影透明度
            static let shadowOffsetX: CGFloat = 0 // iPad阴影X偏移
            static let shadowOffsetY: CGFloat = 2 // iPad阴影Y偏移
            static let animationDuration: Double = 0.3 // iPad动画持续时间
            static let animationSpringResponse: Double = 0.6 // iPad动画弹簧响应
            static let animationSpringDamping: Double = 0.8 // iPad动画弹簧阻尼
            static let pressedScale: CGFloat = 0.95 // iPad按下缩放
            static let hoverScale: CGFloat = 1.02 // iPad悬停缩放
            static let touchAreaPadding: CGFloat = 15 // iPad增大触摸区域
            static let touchAreaMinHeight: CGFloat = 75 // iPad增大最小触摸高度
            static let touchAreaMinWidth: CGFloat = 160 // iPad增大最小触摸宽度
        }
        
        // MARK: - 内容区域iPad配置
        struct ContentSection {
            static let topOffsetPercentage: CGFloat = 0.58 // iPad调整顶部偏移
            static let horizontalPadding: CGFloat = 120 // iPad增大左右边距
            static let verticalSpacing: CGFloat = 35 // iPad增大垂直间距
            static let featureToPriceSpacing: CGFloat = 45 // iPad增大功能与价格间距
            static let priceToButtonSpacing: CGFloat = 45 // iPad增大价格与按钮间距
            static let buttonToAgreementSpacing: CGFloat = 35 // iPad增大按钮与协议间距
            static let maxContentWidth: CGFloat = 600 // iPad限制最大内容宽度
            static let contentAlignment: HorizontalAlignment = .center // iPad居中对齐
        }
        
        // MARK: - 功能列表iPad配置
        struct FeatureList {
            static let iconSize: CGFloat = 42 // iPad增大图标尺寸
            static let fontSize: CGFloat = 19 // iPad增大字体
            static let spacing: CGFloat = 20 // iPad增大间距
            static let verticalSpacing: CGFloat = 12 // iPad增大垂直间距
            static let horizontalPadding: CGFloat = 25 // iPad增大左右边距
            static let lineHeight: CGFloat = 28 // iPad增大行高
            static let maxWidth: CGFloat = 500 // iPad限制最大宽度
        }

        // MARK: - 价格卡片iPad配置
        struct PriceCard {
            static let width: CGFloat = 200 // iPad增大卡片宽度
            static let height: CGFloat = 200 // iPad增大卡片高度
            static let cornerRadius: CGFloat = 20 // iPad增大圆角
            static let spacing: CGFloat = 25 // iPad增大间距
            static let titleFont: CGFloat = 32 // iPad增大标题字体
            static let priceFont: CGFloat = 65 // iPad增大价格字体
            static let unitFont: CGFloat = 28 // iPad增大单位字体
            static let selectedBorderWidth: CGFloat = 12 // iPad增大选中边框
            static let unselectedBorderWidth: CGFloat = 4 // iPad增大未选中边框
            static let shadowRadius: CGFloat = 15 // iPad增大阴影
            static let shadowOpacity: Double = 0.15 // iPad调整阴影透明度
            static let animationScale: CGFloat = 1.05 // iPad增大动画缩放
        }
        
        // MARK: - 订阅按钮iPad配置
        struct SubscribeButton {
            static let height: CGFloat = 60 // iPad增大按钮高度
            static let cornerRadius: CGFloat = 30 // iPad增大圆角
            static let fontSize: CGFloat = 22 // iPad增大字体
            static let horizontalPadding: CGFloat = 60 // iPad增大左右边距
            static let maxWidth: CGFloat = 400 // iPad限制最大宽度
            static let shadowRadius: CGFloat = 10 // iPad增大阴影
            static let shadowOpacity: Double = 0.2 // iPad调整阴影透明度
        }
        
        // MARK: - 协议区域iPad配置
        struct AgreementSection {
            static let fontSize: CGFloat = 16 // iPad增大字体
            static let checkboxSize: CGFloat = 20 // iPad增大复选框
            static let spacing: CGFloat = 20 // iPad增大间距
            static let horizontalPadding: CGFloat = 40 // iPad增大左右边距
            static let maxWidth: CGFloat = 450 // iPad限制最大宽度
        }
        
        // MARK: - 装饰元素iPad配置
        struct DecorationElements {
            static let circle1Size: CGFloat = 180 // iPad增大装饰圆圈
            static let circle1Position: (x: CGFloat, y: CGFloat) = (-80, -50) // iPad调整位置

            static let circle2Size: CGFloat = 220 // iPad增大装饰圆圈
            static let circle2Position: (x: CGFloat, y: CGFloat) = (120, -80) // iPad调整位置

            static let circle3Size: CGFloat = 150 // iPad增大装饰圆圈
            static let circle3Position: (x: CGFloat, y: CGFloat) = (80, 120) // iPad调整位置

            static let circle4Size: CGFloat = 100 // iPad增大装饰圆圈
            static let circle4Position: (x: CGFloat, y: CGFloat) = (-200, -100) // iPad调整位置

            static let circle5Size: CGFloat = 80 // iPad增大装饰圆圈
            static let circle5Position: (x: CGFloat, y: CGFloat) = (250, 150) // iPad调整位置

            static let opacity: Double = 0.08 // iPad调整透明度
        }

        // MARK: - 背景图片iPad配置
        struct BackgroundImage {
            static let offsetX: CGFloat = 0
            static let offsetY: CGFloat = UIScreen.main.bounds.height * 0.18 // iPad调整垂直偏移（屏幕高度的18%）
            static let scaleX: CGFloat = 1.2 // iPad增大水平缩放
            static let scaleY: CGFloat = 1.2 // iPad增大垂直缩放
            static let opacity: Double = 0.9 // iPad调整透明度
            static let blur: CGFloat = 1.0 // iPad增加轻微模糊
        }

        // MARK: - 动画配置iPad优化
        struct AnimationPresets {
            static let springResponse: Double = 0.7 // iPad调整动画响应
            static let springDamping: Double = 0.8 // iPad调整动画阻尼

            struct UserInfo {
                static let delay: Double = 0.1
                static let duration: Double = 0.9 // iPad增长动画时间
                static let offsetY: CGFloat = 60 // iPad增大偏移
                static let scaleStart: CGFloat = 0.85 // iPad调整起始缩放
                static let scaleEnd: CGFloat = 1.0
            }

            struct Content {
                static let delay: Double = 0.3
                static let duration: Double = 0.9 // iPad增长动画时间
                static let offsetY: CGFloat = 100 // iPad增大偏移
                static let staggerInterval: Double = 0.15 // iPad增大错开间隔
            }

            struct Tab {
                static let delay: Double = 0.2
                static let duration: Double = 0.7 // iPad增长动画时间
                static let scaleStart: CGFloat = 0.85 // iPad调整起始缩放
                static let scaleEnd: CGFloat = 1.0
                static let offsetY: CGFloat = 30 // iPad增大偏移
            }
        }
    }
    
    // MARK: - 通用iPad布局配置
    struct General {
        static let minTouchTargetSize: CGFloat = 44 // iPad最小触摸目标尺寸
        static let preferredTouchTargetSize: CGFloat = 60 // iPad推荐触摸目标尺寸
        static let containerMaxWidth: CGFloat = 700 // iPad容器最大宽度
        static let sideMargin: CGFloat = 80 // iPad侧边距
        static let topMargin: CGFloat = 50 // iPad顶部边距
        static let bottomMargin: CGFloat = 50 // iPad底部边距
        static let cardSpacing: CGFloat = 25 // iPad卡片间距
        static let buttonSpacing: CGFloat = 20 // iPad按钮间距
        static let sectionSpacing: CGFloat = 40 // iPad区域间距
    }

    // MARK: - 横竖屏适配配置
    struct Orientation {

        // 横屏专用配置
        struct Landscape {
            static let contentMaxWidth: CGFloat = 800 // 横屏内容最大宽度
            static let sideMargin: CGFloat = 120 // 横屏侧边距
            static let twoColumnLayout: Bool = true // 横屏启用双列布局
            static let compactSpacing: CGFloat = 15 // 横屏紧凑间距
        }

        // 竖屏专用配置
        struct Portrait {
            static let contentMaxWidth: CGFloat = 600 // 竖屏内容最大宽度
            static let sideMargin: CGFloat = 80 // 竖屏侧边距
            static let singleColumnLayout: Bool = true // 竖屏使用单列布局
            static let expandedSpacing: CGFloat = 30 // 竖屏扩展间距
        }
    }
}

/**
 * SwiftUI View扩展
 * 提供iPad布局配置的便捷方法
 */
extension View {
    /// 应用iPad优化布局
    func applyiPadLayout() -> some View {
        self.modifier(iPadLayoutModifier())
    }
    
    /// 应用iPad订阅页面布局
    func applyiPadSubscriptionLayout() -> some View {
        self.modifier(iPadSubscriptionLayoutModifier())
    }
}

/**
 * iPad布局修饰符
 */
struct iPadLayoutModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .frame(maxWidth: DeviceDetection.isPad ? iPadLayoutConfig.General.containerMaxWidth : .infinity)
            .padding(.horizontal, DeviceDetection.isPad ? iPadLayoutConfig.General.sideMargin : 25)
    }
}

/**
 * iPad订阅页面布局修饰符
 */
struct iPadSubscriptionLayoutModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .frame(maxWidth: DeviceDetection.isPad ? iPadLayoutConfig.SubscriptionPage.ContentSection.maxContentWidth : .infinity)
            .padding(.horizontal, DeviceDetection.isPad ? iPadLayoutConfig.SubscriptionPage.ContentSection.horizontalPadding : 25)
    }
}
